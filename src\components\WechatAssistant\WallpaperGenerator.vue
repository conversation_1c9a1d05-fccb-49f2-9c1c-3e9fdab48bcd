<template>
  <view class="wallpaper-generator">
    <!-- 功能介绍 -->
    <view class="intro-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="image" size="32" color="#f1c68e" />
          <text>壁纸生成</text>
        </view>
      </view>
      <view class="intro-text">
        <text>从精选图库中随机选取12张高质量图片，按照您选择的排版方式生成个性化壁纸合集</text>
      </view>
    </view>

    <!-- 自定义标题 -->
    <view class="options-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="edit" size="32" color="#f1c68e" />
          <text>壁纸标题</text>
        </view>
      </view>
      <view class="title-input-container">
        <input
          v-model="wallpaperTitle"
          class="title-input"
          placeholder="请输入壁纸标题（可选，默认为：精选12张高质量壁纸）"
          maxlength="50"
        />
        <view class="input-counter">{{ wallpaperTitle.length }}/50</view>
      </view>
    </view>

    <!-- 排版选择 -->
    <view class="options-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="grid" size="32" color="#f1c68e" />
          <text>排版方式</text>
        </view>
      </view>
      <view class="layout-options">
        <view
          class="layout-item"
          :class="{ active: selectedWallpaperLayout === '2-column' }"
          @click="selectedWallpaperLayout = '2-column'"
        >
          <view class="layout-preview">
            <view class="preview-grid two-column">
              <view class="preview-item"></view>
              <view class="preview-item"></view>
              <view class="preview-item"></view>
              <view class="preview-item"></view>
            </view>
          </view>
          <text class="layout-name">一行两列</text>
          <text class="layout-desc">2列布局，适合手机壁纸</text>
        </view>
        <view
          class="layout-item"
          :class="{ active: selectedWallpaperLayout === '3-column' }"
          @click="selectedWallpaperLayout = '3-column'"
        >
          <view class="layout-preview">
            <view class="preview-grid three-column">
              <view class="preview-item"></view>
              <view class="preview-item"></view>
              <view class="preview-item"></view>
              <view class="preview-item"></view>
              <view class="preview-item"></view>
              <view class="preview-item"></view>
            </view>
          </view>
          <text class="layout-name">一行三列</text>
          <text class="layout-desc">3列布局，更紧凑的排版</text>
        </view>
      </view>
    </view>

    <!-- 壁纸分类选择 -->
    <view class="options-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="category" size="32" color="#f1c68e" />
          <text>壁纸类型</text>
        </view>
      </view>
      <view class="category-options">
        <view
          class="category-item"
          :class="{ active: selectedWallpaperCategory === 'beauty' }"
          @click="selectedWallpaperCategory = 'beauty'"
        >
          <view class="category-icon">👩</view>
          <text class="category-name">美女壁纸</text>
          <text class="category-desc">精选美女图片</text>
        </view>
        <view
          class="category-item"
          :class="{ active: selectedWallpaperCategory === 'mobile' }"
          @click="selectedWallpaperCategory = 'mobile'"
        >
          <view class="category-icon">📱</view>
          <text class="category-name">手机壁纸</text>
          <text class="category-desc">适合手机的壁纸</text>
        </view>
        <view
          class="category-item"
          :class="{ active: selectedWallpaperCategory === 'mixed' }"
          @click="selectedWallpaperCategory = 'mixed'"
        >
          <view class="category-icon">🎨</view>
          <text class="category-name">混合模式</text>
          <text class="category-desc">美女和手机壁纸混合</text>
        </view>
      </view>
    </view>

    <!-- 梦币状态卡片 -->
    <view class="coin-status-section">
      <view class="coin-card">
        <view class="coin-icon">
          <TnIcon name="starry" size="40" color="#f1c68e" />
        </view>
        <view class="coin-content">
          <view class="coin-title">梦币余额</view>
          <view class="coin-desc">
            <view>总余额: {{ totalMxCoin }} 个梦币</view>
          </view>
        </view>
        <view class="coin-cost">
          <text>{{ wechatMxCoin }}币/次</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'

// Props
interface Props {
  selectedWallpaperLayout: string
  selectedWallpaperCategory: string
  wallpaperTitle: string
  loading: boolean
  isVip: boolean
  userMxCoin: number
  userTempCoins: number
  wechatMxCoin: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:selectedWallpaperLayout': [value: string]
  'update:selectedWallpaperCategory': [value: string]
  'update:wallpaperTitle': [value: string]
}>()

// 计算属性
const totalMxCoin = computed(() => {
  return props.userMxCoin + props.userTempCoins
})

// 响应式属性的双向绑定
const selectedWallpaperLayout = computed({
  get: () => props.selectedWallpaperLayout,
  set: (value) => emit('update:selectedWallpaperLayout', value)
})

const selectedWallpaperCategory = computed({
  get: () => props.selectedWallpaperCategory,
  set: (value) => emit('update:selectedWallpaperCategory', value)
})

const wallpaperTitle = computed({
  get: () => props.wallpaperTitle,
  set: (value) => emit('update:wallpaperTitle', value)
})
</script>

<style lang="scss" scoped>
.wallpaper-generator {
  .intro-section,
  .options-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #fff;

    .title-left {
      display: flex;
      align-items: center;

      text {
        margin-left: 15rpx;
      }
    }
  }

  .intro-text {
    text {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.6;
    }
  }

  // 标题输入框样式
  .title-input-container {
    position: relative;

    .title-input {
      width: 100%;
      padding: 20rpx 24rpx;
      background: rgba(255, 255, 255, 0.05);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      color: #ffffff;
      font-size: 28rpx;
      transition: all 0.3s ease;

      &:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: #f1c68e;
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
        font-size: 26rpx;
      }
    }

    .input-counter {
      position: absolute;
      right: 20rpx;
      bottom: -30rpx;
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  // 排版选择样式
  .layout-options {
    display: flex;
    gap: 20rpx;

    .layout-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30rpx 20rpx;
      background: rgba(255, 255, 255, 0.05);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      transition: all 0.3s ease;
      cursor: pointer;

      &.active {
        background: rgba(241, 198, 142, 0.15);
        border-color: #f1c68e;
      }

      .layout-preview {
        margin-bottom: 20rpx;

        .preview-grid {
          display: grid;
          gap: 4rpx;
          width: 80rpx;
          height: 60rpx;

          &.two-column {
            grid-template-columns: repeat(2, 1fr);
          }

          &.three-column {
            grid-template-columns: repeat(3, 1fr);
          }

          .preview-item {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2rpx;
          }
        }
      }

      .layout-name {
        font-size: 26rpx;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8rpx;
      }

      .layout-desc {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.6);
        text-align: center;
      }

      &.active .layout-name {
        color: #f1c68e;
      }

      &.active .layout-desc {
        color: rgba(241, 198, 142, 0.8);
      }
    }
  }

  // 分类选择样式
  .category-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15rpx;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30rpx 20rpx;
      background: rgba(255, 255, 255, 0.05);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      transition: all 0.3s ease;
      cursor: pointer;

      &.active {
        background: rgba(241, 198, 142, 0.15);
        border-color: #f1c68e;
      }

      .category-icon {
        font-size: 40rpx;
        margin-bottom: 15rpx;
      }

      .category-name {
        font-size: 26rpx;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8rpx;
      }

      .category-desc {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.6);
        text-align: center;
      }

      &.active .category-name {
        color: #f1c68e;
      }

      &.active .category-desc {
        color: rgba(241, 198, 142, 0.8);
      }
    }
  }

  // 梦币状态卡片样式
  .coin-status-section {
    margin-bottom: 20rpx;

    .coin-card {
      display: flex;
      align-items: center;
      padding: 24rpx 30rpx;
      border-radius: 12rpx;
      border: 1px solid rgba(241, 198, 142, 0.3);
      background: linear-gradient(135deg, rgba(241, 198, 142, 0.15) 0%, rgba(241, 198, 142, 0.08) 100%);
      transition: all 0.3s ease;
      margin-bottom: 16rpx;

      .coin-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background: rgba(241, 198, 142, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
      }

      .coin-content {
        flex: 1;

        .coin-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8rpx;
        }

        .coin-desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.7);
        }
      }

      .coin-cost {
        padding: 8rpx 16rpx;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20rpx;
        border: 1px solid rgba(255, 255, 255, 0.2);

        text {
          font-size: 24rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
