import { ref, computed, watch } from 'vue'

const vk = uni.vk

export function useWechatAssistant() {
  // 响应式数据
  const articleContent = ref('')
  const loading = ref(false)
  const selectedFunction = ref('generate') // generate, format, rewrite, continue, wallpaper
  const autoGenerateImages = ref(true) // 默认自动配图
  const selectedFormatStyle = ref('general') // 排版风格，默认通用风格
  const selectedStyle = ref('professional') // 写作风格

  // 壁纸生成相关
  const selectedWallpaperLayout = ref('2-column') // 壁纸排版方式：2-column, 3-column
  const selectedWallpaperCategory = ref('beauty') // 壁纸分类：beauty, mobile, mixed
  const wallpaperTitle = ref('') // 壁纸标题
  
  // 梦币相关
  const wechatMxCoin = ref(1) // 公众号助手每次消费梦币数量
  const userMxCoin = ref(0) // 用户永久梦币余额
  const userTempCoins = ref(0) // 用户临时梦币余额（VIP赠送）
  
  // VIP状态 - 全局VIP
  const isVip = ref(false)
  
  // 防止onShow重复调用的标志
  const isOnShowRunning = ref(false)
  
  // 标签页相关
  const currentTab = ref('create') // create, records
  
  // 文章记录相关
  const articles = ref([])
  const articlesLoading = ref(false)
  const searchKeyword = ref('')
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(20)
  const hasMore = ref(true)
  const loadingMore = ref(false)
  
  // 下拉刷新相关
  const refreshing = ref(false)
  
  // 更多菜单相关
  const activeMoreMenu = ref(null)
  
  // 当前文章任务相关
  const processingArticles = ref([]) // 改为数组，支持多个同时进行的任务
  const articlePollingTimer = ref(null)
  
  // 计算属性
  const totalMxCoin = computed(() => {
    return userMxCoin.value + userTempCoins.value
  })
  
  // 检查用户是否有足够梦币（所有用户都需要梦币）
  const canAfford = computed(() => {
    return totalMxCoin.value >= wechatMxCoin.value
  })
  
  // 过滤后的文章列表（排除正在处理的文章）
  const filteredArticles = computed(() => {
    if (processingArticles.value.length === 0) {
      return articles.value
    }
    // 如果有正在处理的文章，从列表中排除它们
    const processingIds = processingArticles.value.map(article => article._id)
    return articles.value.filter(article => !processingIds.includes(article._id))
  })
  
  // 有效的正在处理的文章列表（过滤掉无效数据）
  const validProcessingArticles = computed(() => {
    return processingArticles.value.filter(article =>
      article &&
      article._id &&
      article.status === 'pending'
    )
  })
  
  const totalCost = computed(() => {
    // 所有用户都消费梦币，包括VIP
    return wechatMxCoin.value
  })
  
  const buttonText = computed(() => {
    if (loading.value) {
      return currentTab.value === 'wallpaper' ? '正在生成壁纸...' : '正在生成...'
    }

    if (currentTab.value === 'wallpaper') {
      // 如果梦币不足，显示充值提示
      if (!canAfford.value) {
        return '梦币不足 去充值'
      }
      return '生成壁纸'
    }

    const functionNames = {
      generate: '开始生成',
      format: '开始排版',
      rewrite: '开始改写',
      continue: '开始续写'
    }

    const baseName = functionNames[selectedFunction.value] || '开始生成'

    // 如果梦币不足，显示充值提示
    if (!canAfford.value) {
      return '梦币不足 去充值'
    }

    return baseName
  })
  
  // 更新用户梦币余额
  const updateUserMxCoin = () => {
    const userInfo = vk.getVuex('$user.userInfo')
    console.log('updateUserMxCoin - userInfo:', userInfo)
    userMxCoin.value = userInfo.mx_coin || 0 // 永久梦币
    userTempCoins.value = userInfo.vip_monthly_coins || 0 // 临时梦币
    console.log('updateUserMxCoin - 更新后的值:', {
      userMxCoin: userMxCoin.value,
      userTempCoins: userTempCoins.value,
      totalMxCoin: userMxCoin.value + userTempCoins.value
    })
  }
  
  // 刷新VIP状态
  const refreshVipStatus = async () => {
    try {
      // 更新全局VIP状态
      await vk.myfn.updateVipStatus()
      await vk.userCenter.getCurrentUserInfo({ loading: false })
      isVip.value = vk.myfn.checkVip()
  
      // 更新用户梦币余额
      updateUserMxCoin()
  
      // VIP过期检查由服务端定时任务处理，移除客户端过期提醒
    } catch (error) {
      console.error('刷新VIP状态失败:', error)
    }
  }
  
  // 获取文章列表
  const getArticleList = async (isRefresh = true) => {
    try {
      if (isRefresh) {
        articlesLoading.value = true
        currentPage.value = 1
        hasMore.value = true
      } else {
        loadingMore.value = true
      }

      const res = await vk.callFunction({
        url: 'client/wechat.kh_getArticleList',
        data: {
          pageIndex: currentPage.value,
          pageSize: pageSize.value,
          keyword: searchKeyword.value,
          _t: Date.now() // 添加时间戳防止缓存
        }
      })

      if (res.code === 0) {
        const newArticles = res.data.rows || []
        console.log('获取文章列表成功，数量:', newArticles.length)

        if (isRefresh) {
          articles.value = newArticles
        } else {
          articles.value = [...articles.value, ...newArticles]
        }

        // 使用云函数返回的分页信息
        hasMore.value = res.data.hasMore || false

        if (!isRefresh && newArticles.length > 0) {
          currentPage.value++
        }
      } else {
        console.log('获取文章列表失败:', res.msg)
        vk.toast(res.msg || '获取文章列表失败')
      }
    } catch (error) {
      console.error('获取文章列表失败:', error)
      vk.toast('获取文章列表失败')
    } finally {
      articlesLoading.value = false
      loadingMore.value = false
    }
  }
  
  // 检查正在处理的文章
  const checkProcessingArticles = async () => {
    try {
      const res = await vk.callFunction({
        url: 'client/wechat.kh_getProcessingArticles',
        data: {}
      })
  
      if (res.code === 0 && res.data?.length > 0) {
        processingArticles.value = res.data
        // 如果有正在处理的文章，启动轮询
        startArticlePolling()
      }
    } catch (error) {
      console.error('检查正在处理的文章失败:', error)
    }
  }
  
  // 开始文章状态轮询
  const startArticlePolling = (articleId) => {
    console.log('startArticlePolling 被调用，文章ID:', articleId)

    // 如果已经有轮询在运行，不需要重新启动
    if (articlePollingTimer.value) {
      console.log('轮询已在运行，跳过启动')
      return
    }

    console.log('启动新的轮询定时器')
    articlePollingTimer.value = setInterval(async () => {
      try {
        // 如果没有正在处理的文章，停止轮询
        if (processingArticles.value.length === 0) {
          stopArticlePolling()
          return
        }
  
        // 获取所有正在处理的文章ID
        const articleIds = processingArticles.value.map(article => article._id)
        console.log('轮询文章IDs:', articleIds, '处理中文章:', processingArticles.value)

        const res = await vk.callFunction({
          url: 'client/wechat.kh_getWechatArticleStatus',
          data: { article_ids: articleIds }
        })

        console.log('轮询响应结果:', res)

        if (res.code === 0 && res.data?.length > 0) {
          // 处理每个返回的文章状态
          res.data.forEach((article) => {
            console.log('文章状态更新:', article.title, article.status, article.progress)

            // 更新正在处理的文章列表中对应的文章
            const index = processingArticles.value.findIndex(item => item._id === article._id)
            if (index > -1) {
              processingArticles.value[index] = { ...processingArticles.value[index], ...article }
            }

            // 如果文章完成，处理结果
            if (article.status === 'success') {
              console.log('文章生成成功:', article.title)
              handleArticleSuccess(article)
            } else if (article.status === 'fail') {
              console.log('文章生成失败:', article.title, article.fail_reason)
              handleArticleFail(article)
            }
          })
        } else {
          console.log('轮询无数据，响应:', res)
        }
      } catch (error) {
        console.error('轮询文章状态失败:', error)
      }
    }, 3000) // 每3秒轮询一次
  }
  
  // 停止文章轮询
  const stopArticlePolling = () => {
    if (articlePollingTimer.value) {
      clearInterval(articlePollingTimer.value)
      articlePollingTimer.value = null
    }
  }
  
  // 处理文章生成成功
  const handleArticleSuccess = (article) => {
    console.log('文章生成成功，处理结果:', article.title)

    // 更新用户梦币余额
    updateUserMxCoin()

    // 显示成功提示
    vk.toast('生成成功')

    // 从正在处理的文章列表中移除
    const index = processingArticles.value.findIndex(item => item._id === article._id)
    if (index > -1) {
      processingArticles.value.splice(index, 1)
      console.log('已从处理中列表移除文章:', article.title)
    }

    // 如果没有正在处理的文章了，停止轮询
    if (processingArticles.value.length === 0) {
      console.log('所有文章处理完成，停止轮询')
      stopArticlePolling()
    }

    // 刷新文章列表
    console.log('刷新文章列表以显示新完成的文章')
    getArticleList()
  }
  
  // 处理文章生成失败
  const handleArticleFail = (article) => {
    vk.toast('生成失败: ' + (article.fail_reason || '未知错误'))
  
    // 从正在处理的文章列表中移除
    const index = processingArticles.value.findIndex(item => item._id === article._id)
    if (index > -1) {
      processingArticles.value.splice(index, 1)
    }
  
    // 如果没有正在处理的文章了，停止轮询
    if (processingArticles.value.length === 0) {
      stopArticlePolling()
    }
  
    // 刷新文章列表
    getArticleList()
  }

  // 获取提示词配置
  const getPromptsConfig = () => {
    const config = vk.getVuex("$config.wechat_prompts_config")

    if (!config) {
      console.warn('未找到微信助手提示词配置')
      vk.modal({
        title: '配置缺失',
        content: '未找到微信助手提示词配置，请联系管理员在后台配置相关参数。',
        showCancel: false
      })
      return null
    }

    return config
  }

  // 微信公众号兼容性基础要求（所有风格共有）
  const getWechatCompatibilityRules = () => {
    const config = getPromptsConfig()
    if (!config) {
      throw new Error('无法获取微信兼容性规则配置')
    }
    return config.wechat_compatibility_rules || ''
  }

  // 获取排版风格特定提示词 通用的排版规则
  const getFormatStylePrompt = (style) => {
    const config = getPromptsConfig()
    if (!config) {
      throw new Error('无法获取排版风格配置')
    }

    const baseRules = getWechatCompatibilityRules()

    // 根据风格获取对应的提示词
    const styleKey = `format_style_${style}`
    const styleRules = config[styleKey] || config.format_style_general || ''

    return `${baseRules}\n\n${styleRules}`
  }

  // 获取写作风格提示词
  const getStylePrompt = (style) => {
    const config = getPromptsConfig()
    if (!config) {
      throw new Error('无法获取写作风格配置')
    }
    const styleKey = `writing_style_${style}`
    return config[styleKey] || ''
  }

  // 获取系统消息 - 完整的系统预设
  const getSystemMessage = () => {
    const needImages = autoGenerateImages.value
    const config = getPromptsConfig()

    if (!config) {
      throw new Error('无法获取系统提示词配置')
    }

    if (selectedFunction.value === 'format') {
      // 原文排版的系统预设
      let systemPrompt = config.mode_format_system
      if (!systemPrompt) {
        throw new Error('缺少排版模式系统提示词配置')
      }

      // 添加排版风格要求
      const formatStylePrompt = getFormatStylePrompt(selectedFormatStyle.value)
      systemPrompt += `\n\n${formatStylePrompt}`

      // 配图要求
      if (needImages) {
        systemPrompt += '\n\n附加要求：改写完的文章内容需要加入合适数量的配图描述，至少要有一张，无上限。在文章合适位置显示符合文章内容的图片，配图使用专用的标记[[英文的图片描述]]来表示配图,"英文的图片描述"是配图的英文描述，一定要用英文。'
      } else {
        systemPrompt += '\n\n附加要求：原文中的所有配图，也就是<img>标签，都要在改写后的文章中保留，在合适位置显示。'
      }

      return systemPrompt

    } else {
      // 文章生成、改写、续写的系统预设
      let mode = 'topic'
      if (selectedFunction.value === 'rewrite') mode = 'rewrite'
      if (selectedFunction.value === 'continue') mode = 'continue'

      // 根据功能选择对应的系统提示词
      let systemPrompt
      if (selectedFunction.value === 'rewrite') {
        systemPrompt = config.mode_rewrite_system
        if (!systemPrompt) {
          throw new Error('缺少改写润色系统提示词配置')
        }
      } else if (selectedFunction.value === 'continue') {
        systemPrompt = config.mode_continue_system
        if (!systemPrompt) {
          throw new Error('缺少续写文章系统提示词配置')
        }
      } else {
        // generate 或其他模式使用生成系统提示词
        systemPrompt = config.mode_generate_system
        if (!systemPrompt) {
          throw new Error('缺少文章生成系统提示词配置')
        }
      }

      // 添加写作风格要求
      const stylePrompt = getStylePrompt(selectedStyle.value)
      systemPrompt += `\n\n写作风格要求：\n${stylePrompt}`

      // 添加排版风格要求
      const formatStylePrompt = getFormatStylePrompt(selectedFormatStyle.value)
      systemPrompt += `\n\n${formatStylePrompt}`

      // 配图要求
      if (needImages) {
        systemPrompt += '\n\n附加要求：文章内容需要加入合适数量的符合文章内容的配图描述，至少要有一张，无上限。在文章合适位置显示符合文章内容的图片，配图使用专用的标记[[英文的图片描述]]来表示配图,"英文的图片描述"是配图的英文描述，一定要用英文。'
      }

      console.log('系统提示词:', systemPrompt)
      return systemPrompt
    }
  }



  // 开始生成文章任务
  const formatArticle = async () => {
    // 检查用户是否已登录
    if (!vk.checkToken()) {
      vk.toast('请先登录')
      return
    }

    // 壁纸生成不需要输入内容
    if (currentTab.value !== 'wallpaper' && !articleContent.value.trim()) {
      vk.toast('请输入文章内容')
      return
    }

    // 内容安全检测（壁纸生成不需要检测）
    if (currentTab.value !== 'wallpaper') {
      try {
        const passed = await vk.myfn.textSecCheck(articleContent.value)
        if (!passed) {
          return
        }
      } catch (error) {
        console.error('内容安全检测失败:', error)
        // 如果安全检测失败，继续执行（可能是检测服务不可用）
      }
    }

    // 检查梦币余额（所有用户都需要消费梦币）
    if (totalMxCoin.value < wechatMxCoin.value) {
      vk.confirm({
        title: '梦币不足',
        content: `需要${wechatMxCoin.value}个梦币，当前总余额${totalMxCoin.value}个\n\n是否立即充值梦币？`,
        confirmText: '去充值',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            vk.navigateTo({
              url: '/pages/user/member-rights?tab=coin'
            })
          }
        }
      })
      return
    }

    // 开始创建文章任务
    loading.value = true

    try {
      let taskResult

      if (currentTab.value === 'wallpaper') {
        // 壁纸生成
        taskResult = await vk.callFunction({
          url: 'client/wechat.kh_generateWallpaper',
          data: {
            layout: selectedWallpaperLayout.value,
            category: selectedWallpaperCategory.value,
            title: wallpaperTitle.value,
            mx_coin: totalCost.value
          }
        })
      } else {
        // 文章生成
        // 获取系统消息
        let systemMessage
        try {
          systemMessage = getSystemMessage()
        } catch (configError) {
          console.error('获取系统配置失败:', configError)
          vk.toast('配置获取失败，请稍后重试')
          loading.value = false
          return
        }

        // 创建文章任务
        taskResult = await vk.callFunction({
          url: 'client/wechat.kh_addWechatArticleTask',
          data: {
            original_content: articleContent.value,
            selected_function: selectedFunction.value,
            selected_format_style: selectedFormatStyle.value,
            selected_style: selectedStyle.value,
            auto_generate_images: autoGenerateImages.value,
            system_message: systemMessage,
            user_message: articleContent.value,
            mx_coin: totalCost.value
          }
        })
      }

      if (taskResult.code !== 0) {
        throw new Error(taskResult.msg || '创建任务失败')
      }

      // 关闭loading状态
      loading.value = false

      const isWallpaperGeneration = currentTab.value === 'wallpaper'

      // 切换到文章列表tab
      currentTab.value = 'records'

      // 刷新文章列表
      getArticleList()

      if (isWallpaperGeneration) {
        // 壁纸生成是同步的，直接完成
        vk.toast('壁纸生成成功！')

        // 清空输入内容
        wallpaperTitle.value = ''
      } else {
        // 文章生成需要轮询
        // 添加到正在处理的文章列表
        console.log('添加文章到处理中列表:', taskResult.data)
        console.log('文章ID字段检查:', {
          _id: taskResult.data._id,
          article_id: taskResult.data.article_id
        })
        processingArticles.value.push(taskResult.data)
        console.log('当前处理中文章列表:', processingArticles.value)

        // 开始轮询文章状态
        console.log('开始轮询文章状态，文章ID:', taskResult.data.article_id)
        startArticlePolling(taskResult.data.article_id)

        vk.toast('任务已创建，正在处理中...')

        // 清空输入内容
        articleContent.value = ''
      }

      console.log('任务创建成功:', taskResult.data)

    } catch (error) {
      console.error('创建任务失败:', error)
      vk.toast('创建任务失败: ' + (error?.message || '未知错误'))
      loading.value = false
    }
  }

  // 处理生成按钮点击
  const handleGenerateClick = () => {
    console.log('handleGenerateClick 被调用')
    console.log('canAfford.value:', canAfford.value)
    console.log('totalMxCoin.value:', totalMxCoin.value)
    console.log('wechatMxCoin.value:', wechatMxCoin.value)
    console.log('userMxCoin.value:', userMxCoin.value)
    console.log('userTempCoins.value:', userTempCoins.value)

    if (!canAfford.value) {
      console.log('余额不足，准备跳转到会员页面')
      vk.navigateTo({
        url: '/pages/user/member-rights?tab=coin'
      })
    } else {
      console.log('余额充足，开始生成文章')
      formatArticle()
    }
  }

  // 查看文章详情
  const viewArticle = (article) => {
    // 跳转到文章详情页面
    vk.navigateTo({
      url: `/pages/wechat/article-detail?id=${article._id}`
    })
  }

  // 查看正在处理的文章
  const viewProcessingArticle = (article) => {
    vk.toast('文章正在生成中，请稍候...')
  }

  // 取消正在处理的文章
  const cancelProcessing = async (article) => {
    try {
      const res = await vk.callFunction({
        url: 'client/wechat/kh_cancelWechatArticle',
        data: {
          article_id: article._id
        }
      })

      if (res.code === 0) {
        vk.toast('已取消生成')

        // 从正在处理的文章列表中移除
        const index = processingArticles.value.findIndex(item => item._id === article._id)
        if (index > -1) {
          processingArticles.value.splice(index, 1)
        }

        // 如果没有正在处理的文章了，停止轮询
        if (processingArticles.value.length === 0) {
          stopArticlePolling()
        }

        // 刷新文章列表
        getArticleList()

        // 更新用户梦币余额
        updateUserMxCoin()
      } else {
        vk.toast(res.msg || '取消失败')
      }
    } catch (error) {
      console.error('取消文章生成失败:', error)
      vk.toast('取消失败')
    }
  }

  // 更多菜单相关方法
  const toggleMoreMenu = (articleId) => {
    if (activeMoreMenu.value === articleId) {
      activeMoreMenu.value = null
    } else {
      activeMoreMenu.value = articleId
    }
  }

  // 点击其他地方关闭更多菜单
  const closeMoreMenu = () => {
    activeMoreMenu.value = null
  }

  // 处理菜单操作
  const handleMenuAction = (action, article) => {
    // 先关闭菜单
    closeMoreMenu()

    // 然后执行对应操作
    if (action === 'regenerate') {
      regenerateArticle(article)
    } else if (action === 'delete') {
      deleteArticle(article)
    }
  }

  // 重新生成文章
  const regenerateArticle = (article) => {
    if (!article.original_content && article.function_type !== 'wallpaper') {
      vk.toast('原始内容不存在')
      return
    }

    // 根据文章类型切换到对应的标签页
    if (article.function_type === 'wallpaper') {
      // 壁纸类型，切换到壁纸生成标签页
      currentTab.value = 'wallpaper'

      // 恢复壁纸生成参数
      if (article.processing_params) {
        selectedWallpaperLayout.value = article.processing_params.layout || '2-column'
        selectedWallpaperCategory.value = article.processing_params.category || 'beauty'

        // 从processing_params中恢复自定义标题
        wallpaperTitle.value = article.processing_params.title || ''
      } else {
        wallpaperTitle.value = ''
      }

      vk.toast('已切换到壁纸生成页面，可重新生成')
    } else {
      // 普通文章，切换到创建标签页并填入内容
      currentTab.value = 'create'
      articleContent.value = article.original_content

      // 恢复文章生成参数
      if (article.processing_params) {
        selectedFunction.value = article.processing_params.selected_function || 'generate'
        selectedFormatStyle.value = article.processing_params.selected_format_style || 'general'
        selectedStyle.value = article.processing_params.selected_style || 'professional'
        autoGenerateImages.value = article.processing_params.auto_generate_images || false
      }

      vk.toast('已切换到创建页面，可重新生成')
    }
  }

  // 删除文章
  const deleteArticle = (article) => {
    vk.confirm({
      title: '确认删除',
      content: `确定要删除文章"${article.title || '无标题'}"吗？删除后无法恢复。`,
      confirmText: '删除',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            vk.showLoading('删除中...')

            const deleteRes = await vk.callFunction({
              url: 'client/mx/wechat/kh/deleteArticle',
              data: { article_id: article._id }
            })

            if (deleteRes.code === 0) {
              vk.toast('删除成功')
              // 从列表中移除
              const index = articles.value.findIndex(item => item._id === article._id)
              if (index > -1) {
                articles.value.splice(index, 1)
              }
            } else {
              vk.toast(deleteRes.msg || '删除失败')
            }
          } catch (error) {
            console.error('删除失败:', error)
            vk.toast('删除失败')
          } finally {
            vk.hideLoading()
          }
        }
      }
    })
  }

  // 下拉刷新处理
  const onRefresh = async () => {
    if (currentTab.value !== 'records') return

    refreshing.value = true
    try {
      await getArticleList(true)
      vk.toast('刷新成功')
    } catch (error) {
      console.error('刷新失败:', error)
      vk.toast('刷新失败')
    } finally {
      // 确保刷新状态被重置
      refreshing.value = false
    }
  }

  // 下拉刷新恢复
  const onRefreshRestore = () => {
    refreshing.value = false
  }

  // 滚动到底部加载更多
  const onLoadMore = () => {
    // 只有在文章记录标签页且有更多数据时才加载
    if (currentTab.value === 'records' && hasMore.value && !loadingMore.value && !articlesLoading.value) {
      getArticleList(false)
    }
  }

  return {
    // 响应式数据
    articleContent,
    loading,
    selectedFunction,
    autoGenerateImages,
    selectedFormatStyle,
    selectedStyle,
    selectedWallpaperLayout,
    selectedWallpaperCategory,
    wallpaperTitle,
    wechatMxCoin,
    userMxCoin,
    userTempCoins,
    isVip,
    isOnShowRunning,
    currentTab,
    articles,
    articlesLoading,
    searchKeyword,
    currentPage,
    pageSize,
    hasMore,
    loadingMore,
    refreshing,
    activeMoreMenu,
    processingArticles,
    articlePollingTimer,
    
    // 计算属性
    totalMxCoin,
    canAfford,
    filteredArticles,
    validProcessingArticles,
    totalCost,
    buttonText,
    
    // 方法
    updateUserMxCoin,
    refreshVipStatus,
    getArticleList,
    checkProcessingArticles,
    startArticlePolling,
    stopArticlePolling,
    handleArticleSuccess,
    handleArticleFail,
    getPromptsConfig,
    getWechatCompatibilityRules,
    getFormatStylePrompt,
    getStylePrompt,
    getSystemMessage,
    formatArticle,
    handleGenerateClick,
    viewArticle,
    viewProcessingArticle,
    cancelProcessing,
    toggleMoreMenu,
    closeMoreMenu,
    handleMenuAction,
    regenerateArticle,
    deleteArticle,
    onRefresh,
    onRefreshRestore,
    onLoadMore
  }
}
