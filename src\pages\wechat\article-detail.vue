<template>
  <view class="article-detail-container">
    <!-- 导航栏 -->
    <TnNavbar
      fixed
      :bottom-shadow="false"
      bg-color="transparent"
      title-color="#fff"
      @back="vk.navigateBack()"
    >
      <template #default>文章详情</template>
    </TnNavbar>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner">
          <text class="loading-icon">⏳</text>
        </view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 文章内容 -->
      <view v-else-if="article" class="article-content">
        <!-- 文章头部卡片 -->
        <view class="header-card">
          <!-- 标题 -->
          <view class="title-section">
            <view class="article-title">{{ article.title || '无标题' }}</view>
          </view>

          <!-- 元信息行：时间、字数、状态 -->
          <view class="meta-section">
            <view class="meta-row">
              <view class="meta-left">
                <view class="meta-item">
                  <text class="meta-icon">⏰</text>
                  <text class="meta-text">{{ formatTime(article._add_time) }}</text>
                </view>
                <view class="meta-item">
                  <text class="meta-icon">📄</text>
                  <text class="meta-text">{{ article.word_count || 0 }}字</text>
                </view>
                <view v-if="article.mx_coin && article.mx_coin > 0" class="meta-item">
                  <text class="meta-icon">💰</text>
                  <text class="meta-text">{{ article.mx_coin }}</text>
                </view>
                <!-- 排版风格已移至导航栏显示 -->
              </view>
              <view class="meta-right">
                <view class="status-badge" :class="article.status">
                  <text class="status-icon">{{ getStatusIcon(article.status) }}</text>
                  <text>{{ getStatusText(article.status) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 生成结果卡片 -->
        <view v-if="article.status === 'success' && article.content" class="result-card">
          <view class="card-header">
            <view class="header-left">
              <view class="header-icon">
                <text class="icon-text">✨</text>
              </view>
              <text class="header-title">生成结果</text>
              <!-- 排版风格显示 -->
              <view v-if="article.format_style" class="style-badge">
                <text class="style-icon">{{ getFormatStyleIcon(article.format_style) }}</text>
                <text class="style-name">{{ getFormatStyleName(article.format_style) }}</text>
              </view>
            </view>
            <view class="header-actions">
              <view class="action-btn" @click="copyContent">
                <text class="icon-text">📋</text>
              </view>
            </view>
          </view>
          <view class="generated-content">
            <rich-text
              :nodes="processedContent"
              @tap="handleRichTextTap"
              class="rich-text-content"
            ></rich-text>
          </view>
        </view>

        <!-- 原始内容卡片 -->
        <view class="content-card">
          <view class="card-header" @click="showOriginal = !showOriginal">
            <view class="header-left">
              <view class="header-icon">
                <text class="icon-text">📝</text>
              </view>
              <text class="header-title">原始内容</text>
            </view>
            <view class="header-toggle">
              <text class="icon-text">{{ showOriginal ? '▲' : '▼' }}</text>
            </view>
          </view>
          <view v-if="showOriginal" class="original-content">
            <text class="content-text">{{ article.original_content }}</text>
          </view>
        </view>

        <!-- 失败信息卡片 -->
        <view v-if="article.status === 'fail'" class="error-card">
          <view class="card-header">
            <view class="header-left">
              <view class="header-icon error">
                <text class="icon-text">⚠️</text>
              </view>
              <text class="header-title error">失败原因</text>
            </view>
          </view>
          <view class="error-content">
            <text class="error-text">{{ article.fail_reason || '生成失败，请重试' }}</text>
          </view>
        </view>

        <!-- 同步记录卡片 -->
        <view v-if="article.sync_records && article.sync_records.length > 0" class="sync-card">
          <view class="card-header">
            <view class="header-left">
              <view class="header-icon">
                <text class="icon-text">☁️</text>
              </view>
              <text class="header-title">同步记录</text>
            </view>
          </view>
          <view class="sync-records">
            <view v-for="record in article.sync_records" :key="record.sync_time" class="sync-record">
              <view class="record-header">
                <text class="account-name">{{ record.account_name }}</text>
                <view class="sync-status" :class="record.sync_status">
                  {{ getSyncStatusText(record.sync_status) }}
                </view>
              </view>
              <view class="record-time">{{ formatTime(record.sync_time) }}</view>
              <view v-if="record.error_msg" class="record-error">{{ record.error_msg }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 错误状态 -->
      <view v-else class="error-section">
        <view class="error-icon">
          <text class="large-icon">❌</text>
        </view>
        <text class="error-title">文章不存在</text>
        <text class="error-desc">该文章可能已被删除或不存在</text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view v-if="article" class="bottom-actions">
      <!-- 成功状态的操作按钮 -->
      <template v-if="article.status === 'success'">
        <view class="action-button secondary" @click="regenerateArticle">
          <text>重新生成</text>
        </view>

        <view class="action-button secondary" @click="viewSyncHistory">
          <text>同步历史</text>
        </view>

        <view class="action-button primary" @click="syncToWechat">
          <text>同步公众号</text>
        </view>

        <view class="action-button danger icon-only" @click="deleteArticle">
          <text class="icon-text">🗑️</text>
        </view>
      </template>

      <!-- 失败状态的操作按钮 -->
      <template v-else-if="article.status === 'fail'">
        <view class="action-button secondary" @click="regenerateArticle">
          <text>重新生成</text>
        </view>

        <view class="action-button danger icon-only" @click="deleteArticle">
          <text class="icon-text">🗑️</text>
        </view>
      </template>

      <!-- 处理中状态的操作按钮 -->
      <template v-else-if="article.status === 'pending'">
        <view class="action-button secondary disabled">
          <text>生成中...</text>
        </view>

        <view class="action-button danger icon-only" @click="deleteArticle">
          <text class="icon-text">🗑️</text>
        </view>
      </template>
    </view>

    <!-- 公众号选择弹窗 -->
    <WechatAccountSelector
      v-if="article"
      :visible="showAccountSelector"
      :article-id="article._id"
      :article-title="article.title || ''"
      :article-content="article.content || ''"
      @close="showAccountSelector = false"
      @success="handleSyncSuccess"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import TnNavbar from '@tuniao/tnui-vue3-uniapp/components/navbar/src/navbar.vue'
import WechatAccountSelector from '@/components/wechat-account-selector/index.vue'

// 处理参数接口定义
interface ProcessingParams {
  selected_function?: string
  selected_format_style?: string
  selected_style?: string
  auto_generate_images?: boolean
  system_message?: string
  user_message?: string
}

// 文章接口定义
interface Article {
  _id: string
  title?: string
  status: 'pending' | 'success' | 'fail'
  original_content?: string
  content?: string
  _add_time?: number
  _add_time_str?: string
  word_count?: number
  mx_coin?: number
  auto_generate_images?: boolean
  user_id?: string
  sync_records?: SyncRecord[]
  fail_reason?: string
  format_style?: string // 排版风格
  function_type?: string // 功能类型
  selected_style?: string // 写作风格
  processing_params?: ProcessingParams // 处理参数
}

// 同步记录接口定义
interface SyncRecord {
  sync_time: number
  account_name: string
  sync_status: 'draft' | 'published' | 'failed'
  error_msg?: string
}

const vk = uni.vk

// 响应式数据
const article = ref<Article | null>(null)
const loading = ref(true)
const showOriginal = ref(false)
const articleId = ref('')
const showAccountSelector = ref(false)

// 获取文章详情
const getArticleDetail = async () => {
  try {
    loading.value = true

    const res = await vk.callFunction({
      url: 'client/mx/wechat/kh/getArticleDetail',
      data: { article_id: articleId.value }
    })

    if (res.code === 0) {
      article.value = res.data as Article
    } else {
      vk.toast(res.msg || '获取文章详情失败')
    }
  } catch (error) {
    console.error('获取文章详情失败:', error)
    vk.toast('获取文章详情失败')
  } finally {
    loading.value = false
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '生成中',
    success: '已完成',
    fail: '生成失败'
  }
  return statusMap[status] || status
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, string> = {
    pending: '⏳',
    success: '✅',
    fail: '❌'
  }
  return iconMap[status] || '❓'
}

// 获取排版风格图标
const getFormatStyleIcon = (style: string) => {
  const iconMap: Record<string, string> = {
    general: '📄',
    business: '💼',
    magazine: '✨',
    tech: '🚀',
    lifestyle: '🏠',
    academic: '📚',
    creative: '🎨'
  }
  return iconMap[style] || '📄'
}

// 获取排版风格名称
const getFormatStyleName = (style: string) => {
  const nameMap: Record<string, string> = {
    general: '通用风格',
    business: '商务专业',
    magazine: '时尚杂志',
    tech: '科技未来',
    lifestyle: '温馨生活',
    academic: '学术严谨',
    creative: '创意艺术'
  }
  return nameMap[style] || '通用风格'
}

// 处理富文本内容 - 将HTML转换为rich-text组件可识别的节点格式
const processedContent = computed(() => {
  if (!article.value?.content) return []

  try {
    // 简单的HTML解析，将图片section进行特殊处理
    let htmlContent = article.value.content

    // 处理图片容器，给图片section添加特殊的class标识
    htmlContent = htmlContent.replace(
      /<section[^>]*style="text-align:\s*center[^"]*"[^>]*>\s*<img([^>]*)>\s*<\/section>/gi,
      (match, imgAttrs) => {
        // 提取图片的src和alt属性
        const srcMatch = imgAttrs.match(/src="([^"]*)"/)
        const altMatch = imgAttrs.match(/alt="([^"]*)"/)
        const src = srcMatch ? srcMatch[1] : ''
        const alt = altMatch ? altMatch[1] : ''

        return `<div class="image-container-center"><img src="${src}" alt="${alt}" class="center-image" /></div>`
      }
    )

    // 返回处理后的HTML字符串（rich-text会自动解析）
    return htmlContent
  } catch (error) {
    console.error('处理富文本内容失败:', error)
    return article.value.content || ''
  }
})

// 处理富文本点击事件
const handleRichTextTap = (e: any) => {
  console.log('富文本点击事件:', e)
}

// 获取同步状态文本
const getSyncStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '已同步到草稿箱',
    published: '已发布',
    failed: '同步失败'
  }
  return statusMap[status] || status
}

// 格式化时间
const formatTime = (timestamp: number | undefined) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 小于1小时显示分钟
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return minutes < 1 ? '刚刚' : `${minutes}分钟前`
  }

  // 小于24小时显示小时
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  }

  // 小于7天显示天数
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000)
    return `${days}天前`
  }

  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 复制内容
const copyContent = () => {
  if (!article.value?.content) {
    vk.toast('没有可复制的内容')
    return
  }

  // 移除HTML标签，保留换行和格式
  let textContent = article.value.content
    .replace(/<br\s*\/?>/gi, '\n')  // 将<br>标签转换为换行符
    .replace(/<\/p>/gi, '\n\n')     // 将</p>标签转换为双换行符
    .replace(/<[^>]*>/g, '')        // 移除其他HTML标签
    .replace(/\n{3,}/g, '\n\n')     // 将多个连续换行符替换为最多两个
    .trim()                         // 去除首尾空白

  uni.setClipboardData({
    data: textContent,
    success: () => {
      vk.toast('已复制到剪贴板')
    },
    fail: () => {
      vk.toast('复制失败')
    }
  })
}

// 重新生成文章
const regenerateArticle = () => {
  // 检查是否为壁纸类型
  if (article.value?.function_type === 'wallpaper') {
    // 壁纸类型，设置全局参数并跳转到微信助手页面
    const app = getApp()
    const processingParams = article.value.processing_params as any
    const globalParams = {
      isWallpaper: true,
      layout: processingParams?.layout || '2-column',
      category: processingParams?.category || 'beauty',
      title: processingParams?.title || ''
    }

    // 确保globalData存在
    if (!app.globalData) {
      app.globalData = {}
    }
    app.globalData.regenerateWallpaperParams = globalParams
    console.log('设置壁纸全局参数:', globalParams)

    // 直接跳转到微信助手页面，不使用navigateBack
    vk.navigateTo({
      url: '/pages/tools/wechat-assistant',
      success: () => {
        console.log('成功跳转到微信助手页面')
      },
      fail: (err: any) => {
        console.error('跳转失败:', err)
        vk.toast('跳转失败，请重试')
      }
    })
    return
  }

  // 普通文章的重新生成逻辑
  if (!article.value?.original_content) {
    vk.toast('原始内容不存在')
    return
  }

  // 从 processing_params 获取原始生成参数
  const processingParams = article.value.processing_params

  console.log('文章数据:', article.value)
  console.log('processing_params:', processingParams)

  if (!processingParams) {
    vk.toast('无法获取原始生成参数')
    return
  }

  // 构建完整的参数对象，直接使用 processing_params 中的参数
  const params = {
    content: encodeURIComponent(article.value.original_content),
    autoGenerateImages: processingParams.auto_generate_images ?? false,
    selectedFunction: processingParams.selected_function ?? 'format',
    selectedFormatStyle: processingParams.selected_format_style ?? 'general',
    selectedStyle: processingParams.selected_style ?? 'professional'
  }

  console.log('重新生成参数:', params)

  const queryString = Object.keys(params).map(key => `${key}=${params[key as keyof typeof params]}`).join('&')

  // 检查页面栈中是否已经存在微信助手页面
  const pages = getCurrentPages()
  const wechatAssistantPageIndex = pages.findIndex(page =>
    page.route === 'pages/tools/wechat-assistant' ||
    page.route === 'src/pages/tools/wechat-assistant'
  )

  if (wechatAssistantPageIndex !== -1) {
    // 如果已经存在微信助手页面，返回到该页面并传递参数
    const delta = pages.length - wechatAssistantPageIndex - 1

    // 先设置全局参数，供微信助手页面使用
    const app = getApp()
    app.globalData = app.globalData || {}
    const globalParams = {
      content: article.value?.original_content || '',
      autoGenerateImages: processingParams.auto_generate_images ?? false,
      selectedFunction: processingParams.selected_function ?? 'format',
      selectedFormatStyle: processingParams.selected_format_style ?? 'general',
      selectedStyle: processingParams.selected_style ?? 'professional'
    }
    app.globalData.regenerateParams = globalParams
    console.log('设置全局参数:', globalParams)

    // 返回到微信助手页面
    uni.navigateBack({
      delta: delta,
      success: () => {
        // 通知微信助手页面刷新内容
        const eventParams = {
          content: article.value?.original_content || '',
          autoGenerateImages: processingParams.auto_generate_images ?? false,
          selectedFunction: processingParams.selected_function ?? 'format',
          selectedFormatStyle: processingParams.selected_format_style ?? 'general',
          selectedStyle: processingParams.selected_style ?? 'professional'
        }
        console.log('发送事件参数:', eventParams)
        uni.$emit('regenerateArticle', eventParams)
      }
    })
  } else {
    // 如果不存在微信助手页面，正常跳转
    vk.navigateTo({
      url: `/pages/tools/wechat-assistant?${queryString}`
    })
  }
}

// 查看同步历史
const viewSyncHistory = () => {
  if (!article.value?._id) {
    vk.toast('文章ID不存在')
    return
  }

  // 跳转到同步历史页面
  vk.navigateTo({
    url: `/pages/wechat/sync-select?article_id=${article.value._id}`
  })
}

// 同步到公众号
const syncToWechat = () => {
  if (!article.value?.content) {
    vk.toast('文章内容为空')
    return
  }

  if (!article.value?._id) {
    vk.toast('文章ID不存在')
    return
  }

  // 显示公众号选择弹窗
  showAccountSelector.value = true
}

// 处理同步成功
const handleSyncSuccess = (message: string) => {
  vk.toast(message)
  // 可以在这里刷新同步历史或其他操作
}

// 删除文章
const deleteArticle = () => {
  if (!article.value?._id) {
    vk.toast('文章ID不存在')
    return
  }

  // 显示确认对话框
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这篇文章吗？删除后无法恢复。',
    confirmText: '删除',
    confirmColor: '#ff4d4f',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await vk.callFunction({
            url: 'client/wechat.kh_deleteWechatArticle',
            data: { article_id: article.value!._id }
          })

          if (result.code === 0) {
            vk.toast('删除成功')
            // 返回上一页
            setTimeout(() => {
              vk.navigateBack()
            }, 1000)
          } else {
            vk.toast(result.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除文章失败:', error)
          vk.toast('删除失败')
        }
      }
    }
  })
}

// 页面加载
onLoad((options) => {
  if (options?.id) {
    articleId.value = options.id
    getArticleDetail()
  }
})
</script>

<style lang="scss" scoped>
// 排版风格徽章样式
.style-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  margin-left: 16rpx;
  background: rgba(241, 198, 142, 0.15);
  border: 1rpx solid rgba(241, 198, 142, 0.3);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);

  .style-icon {
    font-size: 20rpx;
    line-height: 1;
  }

  .style-name {
    font-size: 20rpx;
    color: #f1c68e;
    font-weight: 500;
    white-space: nowrap;
  }
}



.article-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a1f2b 50%, #2c3e50 100%);
  position: relative;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(241, 198, 142, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
}

.main-content {
  position: relative;
  z-index: 1;
  padding: 24rpx;
  // padding-top: calc(100rpx + var(--status-bar-height));
  padding-bottom: calc(180rpx + env(safe-area-inset-bottom));
}

// 加载状态
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-spinner {
    margin-bottom: 32rpx;
    animation: pulse 2s infinite;
  }

  .loading-text {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

// 文章内容区域
.article-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

// 头部卡片
.header-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);

  .title-section {
    margin-bottom: 20rpx;

    .article-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
      line-height: 1.4;
      letter-spacing: 0.3rpx;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    }
  }

  .meta-section {
    .meta-row {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .meta-left {
        display: flex;
        align-items: center;
        gap: 32rpx;
        flex: 1;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 10rpx;

          .meta-icon {
            font-size: 30rpx;
            line-height: 1;
            opacity: 0.8;
          }

          .meta-text {
            font-size: 26rpx;
            color: rgba(255, 255, 255, 0.75);
            font-weight: 500;
            letter-spacing: 0.2rpx;
          }
        }
      }

      .meta-right {
        flex-shrink: 0;

        .status-badge {
          display: flex;
          align-items: center;
          gap: 6rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 22rpx;
          font-weight: 600;
          white-space: nowrap;

          &.pending {
            background: linear-gradient(135deg, rgba(255, 173, 20, 0.2), rgba(255, 173, 20, 0.1));
            color: #faad14;
            border: 1rpx solid rgba(255, 173, 20, 0.4);
          }

          &.success {
            background: linear-gradient(135deg, rgba(82, 196, 26, 0.2), rgba(82, 196, 26, 0.1));
            color: #52c41a;
            border: 1rpx solid rgba(82, 196, 26, 0.4);
          }

          &.fail {
            background: linear-gradient(135deg, rgba(255, 77, 79, 0.2), rgba(255, 77, 79, 0.1));
            color: #ff4d4f;
            border: 1rpx solid rgba(255, 77, 79, 0.4);
          }

          .status-icon {
            font-size: 20rpx;
          }
        }
      }
    }
  }
}
// 通用卡片样式
.content-card, .result-card, .error-card, .sync-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
  }
}

// 卡片头部
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .header-icon {
      width: 48rpx;
      height: 48rpx;
      background: rgba(241, 198, 142, 0.15);
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &.error {
        background: rgba(255, 71, 87, 0.15);
      }
    }

    .header-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #fff;

      &.error {
        color: #ff4757;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 16rpx;

    .action-btn {
      width: 48rpx;
      height: 48rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(241, 198, 142, 0.2);
        transform: scale(0.95);
      }
    }
  }

  .header-toggle {
    padding: 8rpx;
    border-radius: 8rpx;
    transition: all 0.3s ease;

    &:active {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

// 生成结果卡片
.result-card {
  .generated-content {
    padding: 32rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    margin: 0;
    border-radius: 0 0 24rpx 24rpx;
    font-size: 28rpx;
    line-height: 1.8;
    color: #2c3e50;
    max-height: 70vh;
    overflow-y: auto;
    word-wrap: break-word;
    word-break: break-all;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8rpx;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4rpx;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }

    :deep(p) {
      margin-bottom: 16rpx;
      word-wrap: break-word;
      word-break: break-all;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(h1), :deep(h2), :deep(h3) {
      color: #1a1f2b;
      font-weight: 600;
      margin: 24rpx 0 16rpx;
      word-wrap: break-word;
      word-break: break-all;

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(ul), :deep(ol) {
      padding-left: 32rpx;
      margin-bottom: 16rpx;

      li {
        margin-bottom: 8rpx;
        word-wrap: break-word;
        word-break: break-all;
      }
    }

    :deep(img) {
      max-width: 100%;
      height: auto;
      border-radius: 8rpx;
      margin: 16rpx 0;
    }

    :deep(blockquote) {
      border-left: 4rpx solid #f1c68e;
      padding-left: 16rpx;
      margin: 16rpx 0;
      color: #666;
      font-style: italic;
    }

    // rich-text 组件样式
    .rich-text-content {
      width: 100%;

      // 处理转换后的图片容器
      :deep(.image-container-center) {
        text-align: center;
        margin: 20rpx -32rpx; // 突破父容器padding
        padding: 0;

        .center-image {
          max-width: 100%;
          height: auto;
          border-radius: 8rpx;
          box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
          display: block;
          margin: 0 auto;
        }
      }

      // 通用图片样式（兜底）
      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 8rpx;
        display: block;
        margin: 16rpx auto;
      }
    }
  }
}

// 原始内容卡片
.content-card {
  .original-content {
    padding: 32rpx;
    max-height: 50vh;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8rpx;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 4rpx;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .content-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.85);
      line-height: 1.7;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

// 错误卡片
.error-card {
  border-color: rgba(255, 71, 87, 0.2);

  .error-content {
    padding: 32rpx;

    .error-text {
      font-size: 28rpx;
      color: rgba(255, 71, 87, 0.9);
      line-height: 1.6;
      background: rgba(255, 71, 87, 0.1);
      padding: 24rpx;
      border-radius: 12rpx;
      border: 1px solid rgba(255, 71, 87, 0.2);
    }
  }
}
// 同步记录卡片
.sync-card {
  .sync-records {
    padding: 0 32rpx 32rpx;

    .sync-record {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 16rpx;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background: rgba(255, 255, 255, 0.08);
        transform: scale(0.98);
      }

      .record-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .account-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #fff;
        }

        .sync-status {
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 22rpx;
          font-weight: 500;

          &.draft {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(52, 152, 219, 0.1));
            color: #3498db;
            border: 1rpx solid rgba(52, 152, 219, 0.3);
          }

          &.published {
            background: linear-gradient(135deg, rgba(82, 196, 26, 0.2), rgba(82, 196, 26, 0.1));
            color: #52c41a;
            border: 1rpx solid rgba(82, 196, 26, 0.3);
          }

          &.failed {
            background: linear-gradient(135deg, rgba(255, 77, 79, 0.2), rgba(255, 77, 79, 0.1));
            color: #ff4d4f;
            border: 1rpx solid rgba(255, 77, 79, 0.3);
          }
        }
      }

      .record-time {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 8rpx;
      }

      .record-error {
        font-size: 24rpx;
        color: #ff4757;
        background: rgba(255, 71, 87, 0.1);
        padding: 12rpx 16rpx;
        border-radius: 8rpx;
        border: 1px solid rgba(255, 71, 87, 0.2);
      }
    }
  }
}

// 错误状态
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;

  .error-icon {
    margin-bottom: 32rpx;
    opacity: 0.8;
  }

  .error-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #ff4757;
    margin-bottom: 16rpx;
  }

  .error-desc {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.5;
  }
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background: linear-gradient(to top, rgba(15, 20, 25, 0.98), rgba(15, 20, 25, 0.95));
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.12);
  display: flex;
  gap: 16rpx;
  z-index: 100;

  .action-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    height: 88rpx;
    border-radius: 20rpx;
    font-size: 30rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;

    &.primary {
      background: linear-gradient(135deg, #f1c68e, #e6b87a);
      color: #634738;
      box-shadow: 0 8rpx 24rpx rgba(241, 198, 142, 0.3);

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(241, 198, 142, 0.4);
        background: linear-gradient(135deg, #e6b87a, #dba96b);
      }
    }

    &.secondary {
      background: rgba(52, 152, 219, 0.15);
      color: #3498db;
      border: 2rpx solid rgba(52, 152, 219, 0.3);
      box-shadow: 0 8rpx 24rpx rgba(52, 152, 219, 0.2);

      &:active {
        transform: translateY(2rpx);
        background: rgba(52, 152, 219, 0.25);
        box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
      }
    }

    &.danger {
      background: rgba(255, 77, 79, 0.15);
      color: #ff4d4f;
      border: 2rpx solid rgba(255, 77, 79, 0.3);
      box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.2);

      &:active {
        transform: translateY(2rpx);
        background: rgba(255, 77, 79, 0.25);
        box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
      }
    }

    &.disabled {
      background: rgba(255, 255, 255, 0.05);
      color: rgba(255, 255, 255, 0.4);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      box-shadow: none;
      cursor: not-allowed;

      &:active {
        transform: none;
        background: rgba(255, 255, 255, 0.05);
        box-shadow: none;
      }
    }

    &.icon-only {
      flex: 0 0 88rpx;
      width: 88rpx;
      min-width: 88rpx;
      padding: 0;

      .icon-text {
        font-size: 32rpx;
      }
    }

    text {
      font-size: 30rpx;
      font-weight: 600;
    }
  }
}

// 文字图标样式
.icon-text {
  font-size: 24rpx;
  line-height: 1;
  display: inline-block;
}

.loading-icon {
  font-size: 80rpx;
  animation: pulse 2s infinite;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.large-icon {
  font-size: 120rpx;
  opacity: 0.8;
}
</style>
