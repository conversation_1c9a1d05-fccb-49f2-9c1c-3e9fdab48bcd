"use strict";
var vk = uniCloud.vk; // 全局vk实例
// 涉及的表名
const dbName = {
  //test: "vk-test", // 测试表
};

var db = uniCloud.database(); // 全局数据库引用
var _ = db.command; // 数据库操作符
var $ = _.aggregate; // 聚合查询操作符

var cloudObject = {
  isCloudObject: true, // 标记为云对象模式

  _before: async function () {
    // let { customUtil, uniID, config, pubFun } = this.getUtil(); // 获取工具包
    vk = uniCloud.vk;
    // const cacheManage = vk.getCacheManage();
    // let mxConfig = await cacheManage.get("mx_config");
    // // console.log('云端缓存', mxConfig)
    // apiConfig = mxConfig.api_config;
    // openai_api_key = apiConfig.openai_api_key;
    // openai_url = apiConfig.openai_url;
    // openai_model = apiConfig.openai_model;
  },

  /**
   * 获取配置的通用方法，支持缓存降级
   * @param {string} configKey 配置键名
   * @returns {Promise<Object|null>} 配置对象
   */
  getConfig: async function (configKey) {
    try {
      // 先从缓存获取
      const cacheManage = vk.getCacheManage();
      let mxConfig = await cacheManage.get("mx_config");

      if (mxConfig && mxConfig[configKey]) {
        return mxConfig[configKey];
      }

      // 缓存没有，从数据库获取
      console.log(`缓存中没有${configKey}配置，从数据库获取`);
      let config = await vk.baseDao.findByWhereJson({
        dbName: 'mx-config',
        whereJson: {
          config_key: configKey
        }
      });

      if (config) {
        // 移除不需要的字段
        delete config._id;
        delete config._add_time;
        delete config._add_time_str;
        delete config.config_key;
        return config;
      }

      return null;
    } catch (error) {
      console.error(`获取${configKey}配置失败:`, error);
      return null;
    }
  },

  _after: async function (options) {
    let { err, res } = options;
    if (err) {
      return; // 如果方法抛出错误，直接return;不处理
    }
    return res;
  },

  /**
   * 创建微信助手文章任务
   * @url client/wechat.addWechatArticleTask 前端调用的url参数地址
   * data 请求参数
   * @param {String} original_content 原始输入内容
   * @param {String} selected_function 功能类型：generate/format/rewrite/continue
   * @param {String} selected_format_style 排版风格
   * @param {String} selected_style 写作风格（仅生成功能需要）
   * @param {Boolean} auto_generate_images 是否自动配图
   * @param {String} system_message AI系统提示词
   * @param {String} user_message 用户消息内容
   * @param {Number} mx_coin 消耗的梦币数量
   */
  kh_addWechatArticleTask: async function (data) {
    let { uid } = this.getClientInfo(); // 获取客户端信息
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------

    const {
      original_content,
      selected_function,
      selected_format_style,
      selected_style,
      auto_generate_images,
      system_message,
      user_message,
      mx_coin,
    } = data;

    // 参数校验
    if (
      !original_content ||
      !selected_function ||
      !selected_format_style ||
      !mx_coin
    ) {
      return { code: -1, msg: "参数不完整" };
    }

    try {
      // 使用事务确保数据一致性
      const transaction = await vk.baseDao.startTransaction();

      try {
        // 1. 检查用户梦币余额（包括永久梦币和临时梦币）
        const userInfo = await vk.baseDao.findById({
          db: transaction,
          dbName: "uni-id-users",
          id: uid,
        });

        if (!userInfo) {
          throw new Error("用户不存在");
        }

        const permanentCoins = userInfo.mx_coin || 0;
        const tempCoins = userInfo.vip_monthly_coins || 0;
        const totalCoins = permanentCoins + tempCoins;

        if (totalCoins < mx_coin) {
          throw new Error(`梦币余额不足，当前总余额：${totalCoins}，需要：${mx_coin}`);
        }

        // 2. 优先扣除临时梦币，不足时再扣除永久梦币
        let deductTempCoins = 0;
        let deductPermanentCoins = 0;

        if (tempCoins >= mx_coin) {
          // 临时币足够，全部从临时币扣除
          deductTempCoins = mx_coin;
          deductPermanentCoins = 0;
        } else {
          // 临时币不够，先扣完临时币，再扣永久币
          deductTempCoins = tempCoins;
          deductPermanentCoins = mx_coin - tempCoins;
        }

        // 更新用户梦币
        const updateData = { update_time: Date.now() };
        if (deductTempCoins > 0) {
          updateData.vip_monthly_coins = _.inc(-deductTempCoins);
        }
        if (deductPermanentCoins > 0) {
          updateData.mx_coin = _.inc(-deductPermanentCoins);
        }

        await vk.baseDao.updateById({
          db: transaction,
          dbName: "uni-id-users",
          id: uid,
          dataJson: updateData,
        });

        // 获取更新后的用户信息
        const updatedUserInfo = await vk.baseDao.findById({
          db: transaction,
          dbName: "uni-id-users",
          id: uid,
        });

        // 添加梦币消费记录（与任务系统保持一致）
        await vk.baseDao.add({
          db: transaction,
          dbName: "mx-coin-records",
          dataJson: {
            user_id: uid,
            mx_coin: mx_coin,
            type: 2, // 支出
            balance: updatedUserInfo.mx_coin || 0,
            temp_balance: updatedUserInfo.vip_monthly_coins || 0,
            deduct_temp_coins: deductTempCoins,
            deduct_permanent_coins: deductPermanentCoins,
            comment: "公众号助手-任务消费",
            task_id: null, // 微信助手没有对应的任务ID，设为null
            _add_time: Date.now(),
            _add_time_str: new Date().toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            })
          }
        });

        // 3. 创建文章记录
        const articleData = {
          user_id: uid,
          title: "生成中...",
          content: "",
          original_content: original_content,
          status: "pending",
          progress: "0%",
          auto_generate_images: auto_generate_images,
          mx_coin: mx_coin,
          // 添加梦币扣除详细信息
          deduct_permanent_coins: deductPermanentCoins, // 扣除的永久梦币数量
          deduct_temp_coins: deductTempCoins,           // 扣除的临时梦币数量
          word_count: 0,
          format_style: selected_format_style,
          function_type: selected_function,
          sync_records: [],
          processing_params: {
            selected_function,
            selected_format_style,
            selected_style,
            auto_generate_images,
            system_message,
            user_message,
          },
          processing_start_time: null,
          fail_reason: "",
          _add_time: Date.now(),
          update_time: Date.now(),
        };

        const articleId = await vk.baseDao.add({
          db: transaction,
          dbName: "mx-wechat-articles",
          dataJson: articleData,
        });

        await transaction.commit();

        // 异步调用文章处理函数
        vk.callFunction({
          url: "client/wechat.kh_processWechatArticle",
          data: { article_id: articleId },
          clientInfo: this.getClientInfo(),
        }).then(async (res) => {
          console.log("文章处理结果:", res);
        });

        res.msg = "文章任务创建成功";
        res.data = {
          _id: articleId,
          article_id: articleId,
          status: "pending",
          progress: "0%",
          title: "生成中...",
        };
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      console.error("创建文章任务失败:", error);
      res.code = -1;
      res.msg = error.message || "创建任务失败";
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 处理微信助手文章AI生成
   * @url client/wechat.kh_processWechatArticle 前端调用的url参数地址
   * data 请求参数
   * @param {String} article_id 文章ID
   */
  kh_processWechatArticle: async function (data) {
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------

    console.log("开始处理文章:", data.article_id);

    const { article_id } = data;

    if (!article_id) {
      return { code: -1, msg: "文章ID不能为空" };
    }

    try {
      // 1. 获取文章信息
      const article = await vk.baseDao.findById({
        dbName: "mx-wechat-articles",
        id: article_id,
      });

      if (!article) {
        return { code: -1, msg: "文章不存在" };
      }

      if (article.status !== "pending") {
        return { code: -1, msg: "文章状态不正确" };
      }

      // 2. 更新处理开始时间和进度
      await vk.baseDao.updateById({
        dbName: "mx-wechat-articles",
        id: article_id,
        dataJson: {
          processing_start_time: Date.now(),
          progress: "20%",
          update_time: Date.now(),
        },
      });

      const processingParams = article.processing_params;

      // 3. 调用AI API进行文章生成
      const aiResponse = await this.callAIAPI(processingParams);

      console.log("AI API响应5555:", aiResponse);

      if (!aiResponse.success) {
        // AI调用失败，更新文章状态并退还梦币
        await vk.baseDao.updateById({
          dbName: "mx-wechat-articles",
          id: article_id,
          dataJson: {
            title:'生成失败，请重试！',
            status: "fail",
            fail_reason: aiResponse.error,
            update_time: Date.now(),
          },
        });

        // 退还梦币
        try {
          await this.refundWechatArticleMxCoin(article);
        } catch (refundErr) {
          console.error("退还梦币失败:", refundErr);
        }

        return { code: -1, msg: aiResponse.error };
      }

      // 4. 更新进度
      await vk.baseDao.updateById({
        dbName: "mx-wechat-articles",
        id: article_id,
        dataJson: {
          progress: "60%",
          update_time: Date.now(),
        },
      });

      // 5. 解析AI返回的内容
      const { title, content: formattedContent } = this.parseAIResponse(
        aiResponse.content
      );

      // 6. 处理图片占位符（如果需要自动配图）
      let finalContent = formattedContent;
      if (article.auto_generate_images) {
        await vk.baseDao.updateById({
          dbName: "mx-wechat-articles",
          id: article_id,
          dataJson: {
            progress: "80%",
            update_time: Date.now(),
          },
        });

        finalContent = await this.processImagePlaceholders(formattedContent);
      }

      // 7. 计算字数
      const textContent = finalContent
        ? finalContent.replace(/<[^>]*>/g, "").trim()
        : "";
      const word_count = textContent.length;

      // 8. 更新文章状态为成功
      await vk.baseDao.updateById({
        dbName: "mx-wechat-articles",
        id: article_id,
        dataJson: {
          title: title,
          content: finalContent,
          word_count: word_count,
          status: "success",
          progress: "100%",
          finish_time: Date.now(),
          update_time: Date.now(),
        },
      });

      res.msg = "文章生成成功";
      res.data = {
        article_id: article_id,
        title: title,
        word_count: word_count,
        status: "success",
      };
    } catch (error) {
      console.error("处理文章失败:", error);

      // 更新文章状态为失败
      await vk.baseDao.updateById({
        dbName: "mx-wechat-articles",
        id: article_id,
        dataJson: {
          title: "生成失败，请重试！",
          status: "fail",
          fail_reason: error.message || "处理失败",
          update_time: Date.now(),
        },
      });

      // 如果有文章信息，退还梦币
      try {
        const article = await vk.baseDao.findById({
          dbName: "mx-wechat-articles",
          id: article_id,
        });
        if (article) {
          // 退还梦币
          try {
            await this.refundWechatArticleMxCoin(article);
          } catch (refundErr) {
            console.error("退还梦币失败:", refundErr);
          }
        }
      } catch (refundError) {
        console.error("退还梦币失败:", refundError);
      }

      res.code = -1;
      res.msg = error.message || "处理失败";
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 获取微信文章列表
   * @url client/wechat.kh_getArticleList 前端调用的url参数地址
   * data 请求参数
   * @param {Number} pageIndex 页码(从1开始)
   * @param {Number} pageSize 每页数量
   * @param {String} keyword 搜索关键词(可选)
   */
  kh_getArticleList: async function (data) {
    let { uid } = this.getClientInfo(); // 获取客户端信息
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------

    // 检查用户登录状态
    if (!uid) {
      return { code: -1, msg: "请先登录" };
    }

    let { pageIndex = 1, pageSize = 20, keyword = "" } = data;

    // 参数验证
    pageIndex = Math.max(1, parseInt(pageIndex));
    pageSize = Math.min(50, Math.max(1, parseInt(pageSize))); // 限制每页最多50条

    try {
      // 构建查询条件
      const whereJson = {
        user_id: uid,
      };

      // 关键词搜索(搜索标题和原始内容)
      if (keyword && keyword.trim()) {
        const searchKeyword = keyword.trim();
        whereJson.$or = [
          { title: new RegExp(searchKeyword, "i") },
          { original_content: new RegExp(searchKeyword, "i") },
        ];
      }

      // 使用 selects 方法获取文章列表（包含分页和总数）
      const articles = await vk.baseDao.selects({
        dbName: "mx-wechat-articles",
        whereJson: whereJson,
        sortArr: [{ name: "_add_time", type: "desc" }], // 按创建时间倒序
        pageIndex: pageIndex,
        pageSize: pageSize,
        getCount: true, // 同时获取总数
      });

      // 处理文章数据，确保时间字段正确
      const processedArticles = (articles.rows || []).map((article) => {
        // 如果 _add_time_str 为空，使用 _add_time 生成
        if (!article._add_time_str && article._add_time) {
          article._add_time_str = vk.pubfn.timeFormat(
            article._add_time,
            "MM-dd hh:mm"
          );
        }

        // 如果还是没有时间字符串，使用当前时间
        if (!article._add_time_str) {
          article._add_time_str = vk.pubfn.timeFormat(
            new Date(),
            "MM-dd hh:mm"
          );
        }

        return article;
      });

      // 获取总数（selects方法返回的total）
      const total = articles.total || 0;

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSize);
      const hasMore = pageIndex < totalPages;

      res.msg = "获取成功";
      res.data = {
        rows: processedArticles,
        total: total,
        pageIndex: pageIndex,
        pageSize: pageSize,
        totalPages: totalPages,
        hasMore: hasMore,
      };
    } catch (error) {
      console.error("获取文章列表失败:", error);
      res.code = -1;
      res.msg = "获取失败";
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 调用AI API进行文章生成
   */
  callAIAPI: async function (processingParams) {
    try {
      const { system_message, user_message } = processingParams;

      // 获取API配置
      let apiConfig = await this.getConfig('api_config');

      if (!apiConfig) {
        throw new Error("API配置不存在，请在管理后台配置相关API");
      }

      let { openai_url, openai_api_key, openai_model } = apiConfig;

      if (!openai_url || !openai_api_key || !openai_model) {
        throw new Error("OpenAI API配置不完整，请在管理后台配置OpenAI API Key、URL和模型");
      }

      // 构建请求数据
      const requestData = {
        model: openai_model,
        messages: [
          {
            role: "system",
            content: system_message,
          },
          {
            role: "user",
            content: user_message,
          },
        ],
        temperature: 0.7,
        max_tokens: 16000,
        response_format: {
          "type": "json_object"
        }
      };

      // 调用OpenAI API
      const response = await vk.request({
        url: openai_url,
        method: "POST",
        headers: {
          Authorization: `Bearer ${openai_api_key}`,
          "Content-Type": "application/json",
        },
        data: requestData,
        timeout: 300000, // 5分钟超时
      });

      console.log("OpenAI API响应:", response);

      if (response.choices && response.choices.length > 0) {
        return {
          success: true,
          content: response.choices[0].message.content,
        };
      } else {
        throw new Error("API返回数据格式错误");
      }
    } catch (error) {
      console.error("AI API调用异常:", error);
      return {
        success: false,
        error: error.message || "AI调用异常",
      };
    }
  },

  /**
   * 解析AI返回的内容
   */
  parseAIResponse: function (content) {
    let title = "无标题";
    let formattedContent = content;

    try {
      let parsedResult = null;

      // 方式1: 优先尝试解析标准JSON格式
      try {
        const jsonObjectMatch = content.match(
          /\{[^{}]*"title"[^{}]*"content"[^{}]*\}|\{[^{}]*"content"[^{}]*"title"[^{}]*\}/s
        );
        if (jsonObjectMatch) {
          parsedResult = JSON.parse(jsonObjectMatch[0]);
        }
      } catch (e) {
        // 继续尝试其他方式
      }

      // 方式2: 尝试解析markdown代码块中的JSON
      if (!parsedResult) {
        try {
          const jsonMatch = content.match(/```json\s*(\{[\s\S]*?\})\s*```/);
          if (jsonMatch) {
            parsedResult = JSON.parse(jsonMatch[1]);
          }
        } catch (e) {
          // 继续尝试其他方式
        }
      }

      // 方式3: 宽松的JSON匹配
      if (!parsedResult) {
        try {
          const looseJsonMatch = content.match(
            /\{[\s\S]*?"title"[\s\S]*?"content"[\s\S]*?\}|\{[\s\S]*?"content"[\s\S]*?"title"[\s\S]*?\}/
          );
          if (looseJsonMatch) {
            parsedResult = JSON.parse(looseJsonMatch[0]);
          }
        } catch (e) {
          // 解析失败，使用原始内容
        }
      }

      if (parsedResult && typeof parsedResult === "object") {
        title = parsedResult.title || "无标题";
        formattedContent = parsedResult.content || content;
      }
    } catch (e) {
      console.warn("JSON解析过程出错，使用原始内容:", e);
    }

    return { title, content: formattedContent };
  },

  /**
   * 处理图片占位符
   */
  processImagePlaceholders: async function (content) {
    // 查找所有图片占位符 [[英文描述]]
    const placeholders = content.match(/\[\[([^\]]+)\]\]/g);
    if (!placeholders || placeholders.length === 0) {
      return content;
    }

    let processedContent = content;

    // 逐个处理图片占位符
    for (const placeholder of placeholders) {
      const query = placeholder.replace(/\[\[|\]\]/g, "").trim();

      try {
        const imageUrl = await this.getImageFromPexels(query);
        if (imageUrl) {
          // 替换占位符为图片标签
          const imgTag = `<section style="text-align: center; margin: 20px 0;">
						<img src="${imageUrl}" alt="${query}" style="width: 100%; max-width: 600px; height: auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" />
					</section>`;
          processedContent = processedContent.replace(placeholder, imgTag);
        } else {
          // 如果获取失败，移除占位符
          processedContent = processedContent.replace(placeholder, "");
        }
      } catch (error) {
        console.error(`处理图片占位符失败: ${query}`, error);
        // 如果获取失败，移除占位符
        processedContent = processedContent.replace(placeholder, "");
      }

      // 添加延迟避免请求过快
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    return processedContent;
  },

  /**
   * 从Pexels获取图片
   */
  getImageFromPexels: async function (query) {
    try {
      // 获取API配置
      let apiConfig = await this.getConfig('api_config');

      // 获取Pexels API Key
      let apiKey = apiConfig?.pexels_api_key;

      // 如果没有配置API Key，使用Unsplash作为备用
      if (!apiKey) {
        console.warn("未配置Pexels API Key，使用Unsplash备用方案");
        return await this.getImageFromUnsplash(query);
      }

      // 调用Pexels API
      const response = await vk.request({
        url: "https://api.pexels.com/v1/search",
        method: "GET",
        headers: {
          "Authorization": apiKey
        },
        data: {
          query: query,
          per_page: 1,
          page: 1,
          orientation: 'landscape' // 横向图片更适合文章配图
        }
      });
      console.log("Pexels API响应:", response);

      if (response.photos?.length > 0) {
        const photo = response.photos[0];
        // 返回中等尺寸的图片
        return photo.src.medium || photo.src.original;
      } else {
        console.warn("Pexels API返回空结果，使用Unsplash备用方案");
        return await this.getImageFromUnsplash(query);
      }

    } catch (error) {
      console.warn("获取Pexels图片异常:", error);
      // 出错时使用Unsplash备用方案
      return await this.getImageFromUnsplash(query);
    }
  },

  /**
   * 从Unsplash获取图片（备用方案）
   */
  getImageFromUnsplash: async function (query) {
    try {
      // Unsplash Source API，不需要API Key但功能有限
      const imageUrl = `https://source.unsplash.com/800x600/?${encodeURIComponent(query)}`;
      return imageUrl;
    } catch (error) {
      console.warn("获取Unsplash图片异常:", error);
      return null;
    }
  },

  /**
   * 退还微信文章梦币
   * @param {Object} articleInfo 文章信息
   * @returns {Promise}
   */
  refundWechatArticleMxCoin: async function (articleInfo) {
    try {
      // 如果没有找到文章信息,则返回
      if (!articleInfo) {
        console.log("未找到文章信息");
        return;
      }

      if (!articleInfo || !articleInfo.mx_coin || articleInfo.mx_coin <= 0) {
        return;
      }

      // 获取用户当前梦币信息
      const userInfo = await vk.baseDao.findById({
        dbName: "uni-id-users",
        id: articleInfo.user_id,
        fieldJson: { mx_coin: true, vip_monthly_coins: true }
      });

      const refundAmount = articleInfo.mx_coin;

      // 计算退还策略（按原扣除详情精确退还）
      let refundTempCoins = 0;
      let refundPermanentCoins = 0;

      if (articleInfo.deduct_temp_coins !== undefined && articleInfo.deduct_permanent_coins !== undefined) {
        // 如果有扣除详情，按原路径退还
        refundTempCoins = articleInfo.deduct_temp_coins || 0;
        refundPermanentCoins = articleInfo.deduct_permanent_coins || 0;
      } else {
        // 兼容旧数据：默认全部退还到永久币（mx_coin）
        refundTempCoins = 0;
        refundPermanentCoins = refundAmount;
      }

      // 更新用户梦币
      const updateData = {};
      if (refundTempCoins > 0) {
        updateData.vip_monthly_coins = _.inc(refundTempCoins);
      }
      if (refundPermanentCoins > 0) {
        updateData.mx_coin = _.inc(refundPermanentCoins);
      }

      const newUserInfo = await vk.baseDao.updateById({
        dbName: "uni-id-users",
        id: articleInfo.user_id,
        dataJson: updateData,
        getUpdateData: true,
      });

      // 记录梦币退还（与任务系统保持一致的格式）
      await vk.baseDao.add({
        dbName: "mx-coin-records",
        dataJson: {
          user_id: articleInfo.user_id,
          type: 1, // 1 为增加，2为减少
          mx_coin: articleInfo.mx_coin,
          task_id: null, // 微信文章没有对应的任务ID
          comment: "公众号助手失败退还",
          balance: newUserInfo.mx_coin,
          temp_balance: newUserInfo.vip_monthly_coins || 0,
          refund_temp_coins: refundTempCoins,
          refund_permanent_coins: refundPermanentCoins,
          _add_time: Date.now(),
          _add_time_str: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          })
        },
      });

      console.log(`公众号助手文章生成失败，已退还${articleInfo.mx_coin}个梦币给用户${articleInfo.user_id}`);
      return newUserInfo;
    } catch (err) {
      console.error("退还微信文章梦币失败:", err);
      throw err;
    }
  },

  /**
   * 获取正在处理的文章列表
   * @description 获取当前用户所有状态为 pending 的文章
   */
  kh_getProcessingArticles: async function () {
    let { uid } = this.getParams();
    let res = { code: 0, msg: "", data: [] };

    // 业务逻辑开始-----------------------------------------------------------

    // 检查用户是否已登录
    if (!uid) {
      return { code: -1, msg: "请先登录" };
    }

    try {
      // 查询当前用户所有正在处理的文章
      const processingArticles = await vk.baseDao.select({
        dbName: "mx-wechat-articles",
        whereJson: {
          user_id: uid,
          status: "pending" // 只查询正在处理的文章
        },
        sortArr: [{ name: "_add_time", type: "desc" }], // 按创建时间倒序
        // 返回必要的字段
        fieldJson: {
          _id: true,
          title: true,
          status: true,
          progress: true,
          function_type: true,
          format_style: true,
          auto_generate_images: true,
          processing_start_time: true,
          _add_time: true,
          _add_time_str: true
        }
      });

      // 处理文章数据，确保时间字段正确
      const processedArticles = (processingArticles.rows || []).map(article => {
        // 如果 _add_time_str 为空，使用 _add_time 生成
        if (!article._add_time_str && article._add_time) {
          article._add_time_str = vk.pubfn.timeFormat(
            article._add_time,
            "MM-dd hh:mm"
          );
        }

        // 如果还是没有时间字符串，使用当前时间
        if (!article._add_time_str) {
          article._add_time_str = vk.pubfn.timeFormat(
            new Date(),
            "MM-dd hh:mm"
          );
        }

        // 确保必要字段有默认值
        article.progress = article.progress || "0%";
        article.function_type = article.function_type || "generate";
        article.format_style = article.format_style || "general";

        return article;
      });

      res.msg = "获取正在处理的文章成功";
      res.data = processedArticles;

    } catch (error) {
      console.error("获取正在处理的文章失败:", error);
      res = { code: -1, msg: "获取正在处理的文章失败" };
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 删除微信文章
   * @description 删除用户的微信文章，如果是失败状态会退还梦币
   */
  kh_deleteWechatArticle: async function () {
    let { uid } = this.getClientInfo(); // 获取客户端信息
    let { article_id } = this.getParams();
    let res = { code: 0, msg: "", data: {} };

    // 业务逻辑开始-----------------------------------------------------------

    if (!article_id) {
      return { code: -1, msg: "文章ID不能为空" };
    }

    try {
      // 查找文章
      const article = await vk.baseDao.findByWhereJson({
        dbName: "mx-wechat-articles",
        whereJson: {
          _id: article_id,
          user_id: uid
        }
      });

      if (!article) {
        return { code: -1, msg: "文章不存在或无权限" };
      }

      // 如果是失败状态的文章，退还梦币
      if (article.status === 'fail' && article.mx_coin && article.mx_coin > 0) {
        try {
          await this.refundWechatArticleMxCoin(article);
        } catch (refundErr) {
          console.error("退还梦币失败:", refundErr);
        }
      }

      // 删除文章
      await vk.baseDao.deleteById({
        dbName: "mx-wechat-articles",
        id: article_id
      });

      res.msg = "删除成功";

    } catch (error) {
      console.error("删除文章失败:", error);
      res = { code: -1, msg: "删除失败" };
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 获取微信助手文章状态
   * @description 根据文章ID数组获取文章状态信息
   * @param {Array} article_ids 文章ID数组
   */
  kh_getWechatArticleStatus: async function (data) {
    let { uid } = this.getClientInfo();
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------

    // 参数校验
    if (!data.article_ids || !Array.isArray(data.article_ids) || data.article_ids.length === 0) {
      return { code: -1, msg: "参数错误" };
    }

    try {
      // 查询指定文章的状态
      const result = await vk.baseDao.select({
        dbName: "mx-wechat-articles",
        whereJson: {
          user_id: uid,
          _id: _.in(data.article_ids)
        },
        // 只返回必要的字段
        fieldJson: {
          _id: true,
          title: true,
          status: true,
          progress: true,
          fail_reason: true,
          word_count: true,
          processing_start_time: true,
          finish_time: true,
          _add_time: true,
          update_time: true
        }
      });

      console.log('查询文章状态结果，数量:', result.rows?.length || 0);
      res.data = result.rows;

    } catch (err) {
      console.error('查询文章状态失败:', err);
      return { code: -1, msg: "查询失败" };
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 获取微信助手提示词配置
   * @url client/wechat.kh_getWechatPrompts 前端调用的url参数地址
   */
  kh_getWechatPrompts: async function (data) {
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------

    try {
      // 获取提示词配置
      let promptsConfig = await this.getConfig('wechat_prompts_config');

      if (promptsConfig) {
        res.data = promptsConfig;
        res.msg = "获取提示词配置成功";
      } else {
        // 返回默认配置
        res.data = this.getDefaultWechatPrompts();
        res.msg = "使用默认提示词配置";
      }

    } catch (err) {
      console.error('获取提示词配置失败:', err);
      // 出错时返回默认配置
      res.data = this.getDefaultWechatPrompts();
      res.msg = "获取配置失败，使用默认配置";
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 获取默认的微信助手提示词配置
   */
  getDefaultWechatPrompts: function () {
    return {
      // 微信兼容性规则
      wechat_compatibility_rules: `
- 文章内容是一行的HTML原生代码，使用HTML实体，无换行，无空格，无冗余反斜杠！保持良好的兼容性。
- 微信公众号富文本，不要使用不支持的元素。
- table标签中使用tr，td标签，不要用th标签。
- 使用<section>标签，而不是<div>标签，都是使用内联样式，不要嵌套。
- 原文中的<img>标签请保留下来，并显示在合适的位置上，除了原文的<img>标签外，不要新增其他的<img>标签！
- 使用内联样式增加<p>标签的间距。
- 列表请使用<p>标签来实现一样的效果，不用<ul><ol>标签来创建列表，每个列表项为单独的一行。
- 每个段落不要超过3句话！
- 避免使用微信公众号不支持的CSS属性，如：position:fixed、position:absolute、float等。
- 确保所有样式都是微信公众号富文本编辑器兼容的格式。
`,

      // 排版风格提示词
      format_style_general: `
排版风格要求 - 通用风格：
- 采用现代卡片式布局，使用圆角边框(border-radius: 12px)和微妙阴影效果。
- 整体使用随机的淡色背景，确保在微信公众号中显示正常。
- 重要内容使用高亮背景或边框装饰，突出重点信息。
- 使用内联样式为<p>标签设置合适间距(margin: 16px 0)和行高(line-height: 1.7)。
- 配色方案：主色调使用深蓝或深灰，辅助色使用温暖的橙色或绿色作为点缀。
- 文字颜色层次分明：标题用深色，正文用中等灰色，辅助信息用浅灰色。
- 添加适量的emoji表情作为视觉点缀，但不要过度使用。
- 字体大小：标题18-20px，正文14-16px，辅助信息12-14px。
- 关键词和重要信息使用加粗或颜色高亮处理。
- 整体风格现代简约，注重视觉层次和阅读体验。
- 避免使用复杂的CSS选择器，只使用内联样式。`,

      format_style_business: `
排版风格要求 - 商务专业：
- 采用严谨的商务风格，整体使用随机的淡色背景(如#ffffff、#f8fafc、#f1f5f9等)，确保专业感
- 正文字体16px，颜色为深灰色(#2d3748)，行高1.8，段落间距20px，字体工整规范
- 重要信息使用专业的卡片样式：深蓝色左边框(border-left: 4px solid #1e40af)，浅蓝色背景(#f0f9ff)，内边距18px，微妙阴影(box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1))
- 使用简洁的分割线：颜色为浅灰色(#e2e8f0)，高度1px，两端留白20px，营造专业的视觉分隔
- 数据和关键指标使用突出的展示样式：浅蓝色背景(#eff6ff)，深蓝色文字(#1e40af)，圆角边框(border-radius: 6px)，内边距12px，加粗字体
- 列表项目使用规整的编号或项目符号，深蓝色标记，项目间距统一
- 表格样式严格规范：表头使用深蓝色背景(#1e40af)，白色文字，表格边框使用浅灰色(#e2e8f0)，行间距适中
- 引用内容使用专业样式：浅灰色背景(#f7fafc)，左侧深蓝色装饰条，斜体字，内边距16px
- 按钮和链接使用商务蓝色(#1e40af)，悬停效果为深蓝色(#1e3a8a)
- 图片使用简洁的边框，无圆角，体现商务的严谨性
- 整体风格简洁专业，避免过多装饰元素，体现商务严谨性，每个元素都传达权威和可信度`,

      // 写作风格提示词
      writing_style_professional: '专业严谨，用词准确，逻辑清晰，适合商务和学术场景',
      writing_style_casual: '轻松随意，语言亲切，贴近生活，容易引起读者共鸣',
      writing_style_humorous: '幽默风趣，语言生动，适当使用网络用语和段子',
      writing_style_emotional: '情感丰富，感染力强，能够触动读者内心',
      writing_style_tech: '技术专业，术语准确，逻辑严密，适合科技类内容',
      writing_style_lifestyle: '生活化表达，实用性强，贴近日常生活场景',

      // 功能模式提示词
      mode_generate_system: `你是爆款公众号文章写作专家，模拟人类写作，用口语化的表达。基于先进人工智能技术开发的专业虚拟助手，结合了大数据分析与自然语言处理能力，深度学习了海量优质微信公众号文章，从而具备了洞察读者喜好、创作吸引眼球标题及撰写高质量内容的能力。
标题要求：标题采用爆款标题模板，纯文字，可以使用以下的标题组合模板中的一个即可：1.数字+痛点+解决方案 2.悬念+热点+情感共鸣 3.危机预警+利益诱惑 4.身份标签+反常识结论 5.热点借势+圈层术语

注意：请返回一个标准的JSON数据{"title":"文章标题","content":"文章内容"}，避免格式错误或者解析错误，文章内容中不要包含标题！`,

      mode_rewrite_system: `你是专业的文章改写润色专家，擅长对现有文章进行深度改写和润色，提升文章的表达质量、可读性和吸引力。

遵循以下要求：
- 保持原文的核心观点和主要信息不变
- 优化文章结构，使逻辑更清晰、层次更分明
- 提升语言表达的生动性和感染力
- 增强文章的可读性和吸引力
- 适当调整段落结构，使文章更符合微信公众号阅读习惯
- 保留原文中有价值的数据、案例和引用
- 优化标题，使其更具吸引力和传播性
- 确保改写后的内容符合微信公众号发布规范

注意：请返回一个标准的JSON数据{"title":"文章标题","content":"文章内容"}，避免格式错误或者解析错误，文章内容中不要包含标题！`,

      mode_continue_system: `你是专业的文章续写专家，擅长基于已有的文章开头或片段，继续创作完成整篇文章，保持风格一致性和内容连贯性。

遵循以下要求：
- 仔细分析已有内容的写作风格、语调和表达方式
- 保持与原文风格的高度一致性
- 确保续写内容与前文逻辑连贯、主题统一
- 根据前文内容合理推断文章的发展方向
- 保持适当的篇幅，确保文章完整性
- 如果原文有明确的结构框架，按照该框架继续展开
- 确保续写后的完整文章具有良好的可读性
- 为完整的文章起一个合适的标题

注意：请返回一个标准的JSON数据{"title":"文章标题","content":"文章内容"}，避免格式错误或者解析错误，文章内容中不要包含标题！`,

      mode_format_system: `你是一位专业的公众号文章排版设计师，擅长对文章进行美观的排版和样式优化，而不改变原文内容。

遵循以下要求：
- 文章标题是纯文字
- 不要改变原文的任何文字内容，保持原文的完整性
- 不要添加或删除任何段落、句子或词语
- 保留所有原有的图片标签和链接
- 添加适当的段落间距，使文章结构清晰
- 使用合适的字体大小、行高和颜色，提高可读性
- 美化列表、引用和其他特殊元素
- 确保排版后的内容在移动设备上也能良好显示
- 保持整体风格简洁、专业、美观
- 起一个爆款标题

注意：请返回一个标准的JSON数据{"title":"文章标题","content":"文章内容"}，避免格式错误或者解析错误，文章内容中不要包含标题！`
    };
  },

  /**
   * 生成壁纸
   * @url client/wechat.kh_generateWallpaper 前端调用的url参数地址
   * data 请求参数
   * @param {String} layout 排版方式：2-column/3-column
   * @param {String} category 壁纸分类：beauty/mobile/mixed
   * @param {Number} mx_coin 消耗的梦币数量
   */
  kh_generateWallpaper: async function (data) {
    let { uid } = this.getClientInfo(); // 获取客户端信息
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------

    const { layout, category, title, mx_coin } = data;

    // 参数验证
    if (!layout || !category) {
      return { code: -1, msg: "排版方式和壁纸分类不能为空" };
    }

    if (!['2-column', '3-column'].includes(layout)) {
      return { code: -1, msg: "排版方式参数错误" };
    }

    if (!['beauty', 'mobile', 'mixed'].includes(category)) {
      return { code: -1, msg: "壁纸分类参数错误" };
    }

    try {
      // 开启事务
      const transaction = await vk.baseDao.startTransaction();

      try {
        // 1. 检查用户梦币余额（包括永久梦币和临时梦币）
        const userInfo = await vk.baseDao.findById({
          db: transaction,
          dbName: "uni-id-users",
          id: uid,
        });

        if (!userInfo) {
          throw new Error("用户不存在");
        }

        const permanentCoins = userInfo.mx_coin || 0;
        const tempCoins = userInfo.vip_monthly_coins || 0;
        const totalCoins = permanentCoins + tempCoins;

        if (totalCoins < mx_coin) {
          throw new Error(`梦币余额不足，当前总余额：${totalCoins}，需要：${mx_coin}`);
        }

        // 2. 优先扣除临时梦币，不足时再扣除永久梦币
        let deductTempCoins = 0;
        let deductPermanentCoins = 0;

        if (tempCoins >= mx_coin) {
          // 临时币足够，全部从临时币扣除
          deductTempCoins = mx_coin;
          deductPermanentCoins = 0;
        } else {
          // 临时币不够，先扣完临时币，再扣永久币
          deductTempCoins = tempCoins;
          deductPermanentCoins = mx_coin - tempCoins;
        }

        // 3. 更新用户梦币
        const updateData = {};
        if (deductTempCoins > 0) {
          updateData.vip_monthly_coins = _.inc(-deductTempCoins);
        }
        if (deductPermanentCoins > 0) {
          updateData.mx_coin = _.inc(-deductPermanentCoins);
        }

        const updatedUserInfo = await vk.baseDao.updateById({
          db: transaction,
          dbName: "uni-id-users",
          id: uid,
          dataJson: updateData,
          getUpdateData: true,
        });

        // 4. 添加梦币消费记录
        await vk.baseDao.add({
          db: transaction,
          dbName: "mx-coin-records",
          dataJson: {
            user_id: uid,
            mx_coin: mx_coin,
            type: 2, // 支出
            balance: updatedUserInfo.mx_coin || 0,
            temp_balance: updatedUserInfo.vip_monthly_coins || 0,
            deduct_temp_coins: deductTempCoins,
            deduct_permanent_coins: deductPermanentCoins,
            comment: "壁纸生成消费",
            task_id: null,
            _add_time: Date.now(),
            _add_time_str: new Date().toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            })
          }
        });

        // 5. 根据分类获取图片
        const images = await this.getWallpaperImages(category);

        if (images.length < 12) {
          await transaction.rollback();
          return { code: -1, msg: `图片数量不足，当前只有${images.length}张图片，需要至少12张` };
        }

        // 3. 随机选取12张图片
        const selectedImages = this.shuffleArray(images).slice(0, 12);

        // 4. 生成壁纸HTML内容
        const wallpaperContent = this.generateWallpaperHTML(selectedImages, layout, title);

        // 5. 创建文章记录
        const articleData = {
          user_id: uid,
          title: title || `𝙬𝙖𝙡𝙡𝙥𝙖𝙥𝙚𝙧｜绝美高质量壁纸`,
          content: wallpaperContent,
          original_content: `壁纸生成 - ${this.getCategoryName(category)} - ${layout === '2-column' ? '两列' : '三列'}排版`,
          word_count: 0, // 壁纸没有字数
          status: "success",
          progress: "100%",
          function_type: "wallpaper",
          format_style: layout,
          selected_function: "wallpaper",
          selected_format_style: layout,
          selected_style: category,
          auto_generate_images: false, // 壁纸本身就是图片
          mx_coin: mx_coin, // 消费的梦币数量
          deduct_temp_coins: deductTempCoins, // 扣除的临时币数量
          deduct_permanent_coins: deductPermanentCoins, // 扣除的永久币数量
          processing_params: {
            layout: layout,
            category: category,
            title: title, // 保存自定义标题用于重新生成
            selected_images: selectedImages.map(img => ({
              _id: img._id,
              url: img.url,
              display_name: img.display_name
            }))
          },
          processing_start_time: Date.now(),
          finish_time: Date.now(),
          _add_time: Date.now(),
          update_time: Date.now(),
        };

        const articleId = await vk.baseDao.add({
          db: transaction,
          dbName: "mx-wechat-articles",
          dataJson: articleData,
        });

        await transaction.commit();

        res.msg = "壁纸生成成功";
        res.data = {
          _id: articleId,
          article_id: articleId,
          title: articleData.title,
          status: "success",
          content: wallpaperContent
        };

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      console.error("生成壁纸失败:", error);
      res.code = -1;
      res.msg = error.message || "生成壁纸失败";
    }

    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },

  /**
   * 根据分类获取壁纸图片
   */
  getWallpaperImages: async function (category) {
    let categoryNames = [];

    // 根据分类确定要查询的分类名称
    if (category === 'beauty') {
      categoryNames = ['美女壁纸'];
    } else if (category === 'mobile') {
      categoryNames = ['手机壁纸'];
    } else if (category === 'mixed') {
      categoryNames = ['美女壁纸', '手机壁纸'];
    }

    try {
      // 首先获取分类ID
      const categories = await vk.baseDao.select({
        dbName: "vk-files-categories",
        whereJson: {
          name: _.in(categoryNames)
        },
        fieldJson: {
          _id: true,
          name: true
        }
      });

      if (!categories.rows || categories.rows.length === 0) {
        console.log('未找到对应的图片分类:', categoryNames);
        return [];
      }

      const categoryIds = categories.rows.map(cat => cat._id);

      // 根据分类ID查询图片
      const images = await vk.baseDao.select({
        dbName: "vk-files",
        whereJson: {
          category_id: _.in(categoryIds),
          type: "image", // 只查询图片类型
          status: 0 // 只查询启用状态的图片
        },
        fieldJson: {
          _id: true,
          url: true,
          display_name: true,
          category_id: true
        },
        pageSize: 10000 // 获取足够多的图片供随机选择
      });

      return images.rows || [];

    } catch (error) {
      console.error('获取壁纸图片失败:', error);
      return [];
    }
  },

  /**
   * 数组随机排序
   */
  shuffleArray: function (array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  /**
   * 生成壁纸HTML内容
   */
  generateWallpaperHTML: function (images, layout, customTitle = '') {
    const columns = layout === '2-column' ? 2 : 3;
    const title = customTitle || `𝙬𝙖𝙡𝙡𝙥𝙖𝙥𝙚𝙧｜绝美${images.length}张高质量壁纸`;

    let html = `<section style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">`;

    // 添加标题
    if (title) {
      html += `<h2 style="text-align: center; color: #333; margin-bottom: 20px; font-size: 24px;">${title}</h2>`;
    }

    html += `<section style="display: grid; grid-template-columns: repeat(${columns}, 1fr); gap: 15px; margin-bottom: 20px;">`;

    images.forEach((image, index) => {
      html += `<section style="border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"><img src="${image.url}" alt="壁纸${index + 1}" style="width: 100%; height: 300px; object-fit: cover; display: block;" loading="lazy" /></section>`;
    });

    html += `</section><section style="text-align: center; color: #666; font-size: 14px; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;"><p style="margin: 0;">✨ ${title} ✨</p><p style="margin: 5px 0 0 0;">长按图片保存到相册，让你的设备更加个性化</p></section></section>`;

    return html;
  },

  /**
   * 获取分类中文名称
   */
  getCategoryName: function (category) {
    const names = {
      'beauty': '美女',
      'mobile': '手机',
      'mixed': '精选'
    };
    return names[category] || '精选';
  },

};

module.exports = cloudObject;
